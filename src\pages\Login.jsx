import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './Login.css';
import { auth } from '../config/firebaseConfig';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { signInWithEmailAndPassword, sendPasswordResetEmail } from "firebase/auth";
import { checkLoginLockout, recordLoginFailure, clearLoginLockout } from '../utils/LockoutManager';

function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const lockoutStatus = checkLoginLockout();
    if (lockoutStatus.isLocked) {
      setErrorMessage(lockoutStatus.message);
      setLoading(true);
    } else {
      setLoginAttempts(lockoutStatus.attempts);
    }
  }, []);

  // التحقق من صحة ال��يميل وإظهار خانة كلمة المرور
  const handleEmailChange = (e) => {
    const emailValue = e.target.value;
    setEmail(emailValue);
    
    // التحقق من صحة الإيميل
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (emailValue.trim() && emailRegex.test(emailValue)) {
      setShowPassword(true);
    } else {
      setShowPassword(false);
      setPassword(''); // مسح كلمة المرور إذا كان الإيميل غير صحيح
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setErrorMessage('');
    setSuccessMessage('');
    setLoading(true);

    if (!email.trim() || !password.trim()) {
      setErrorMessage('يرجى إدخال البريد الإلكتروني وكلمة المرور.');
      setLoading(false);
      return;
    }

    // التحقق من حالة القفل قبل المحاولة
    const lockoutStatus = checkLoginLockout();
    if (lockoutStatus.isLocked) {
      setErrorMessage(lockoutStatus.message);
      setLoading(false);
      return;
    }

    try {
      const userCredential = await signInWithEmailAndPassword(auth, email.trim(), password);
      const user = userCredential.user;
      console.log('تم تسجيل الدخول بنجاح:', user);
      // مسح بيانات القفل عند النجاح
      clearLoginLockout();
      setLoginAttempts(0);
      navigate('/dashboard', { replace: true });
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error.code, error.message);

      // تسجيل المحاولة الفاشلة
      const failureResult = recordLoginFailure(loginAttempts);
      setLoginAttempts(failureResult.attempts);

      if (failureResult.isLocked) {
        setErrorMessage(failureResult.message);
        setLoading(true);
        return;
      }

      let userFacingMessage = 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.';
      switch (error.code) {
        case 'auth/invalid-credential':
          userFacingMessage = 'البريد الإلكتروني أو كلمة المرور غير صحيحة. يرجى التحقق من البيانات أو إعادة تعيين كلمة المرور.';
          break;
        case 'auth/invalid-email':
          userFacingMessage = 'صيغة البريد الإلكتروني غير صحيحة.';
          break;
        case 'auth/user-disabled':
          userFacingMessage = 'تم تعطيل حساب المستخدم هذا.';
          break;
        case 'auth/too-many-requests':
          userFacingMessage = '��م حظر تسجيل الدخول مؤقتًا بسبب محاولات كثيرة جدًا. يرجى المحاولة لاحقًا.';
          break;
        default:
          userFacingMessage = 'حدث خطأ غير متوقع. يرجى التحقق من اتصال الإنترنت والمحاولة لاحقًا.';
      }
      setErrorMessage(userFacingMessage);
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordReset = async () => {
    if (!email.trim()) {
      setErrorMessage('يرجى إدخال البريد الإلكتروني أولاً.');
      return;
    }
    setErrorMessage('');
    setSuccessMessage('');
    setLoading(true);
    try {
      await sendPasswordResetEmail(auth, email.trim());
      setSuccessMessage('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني. يرجى التحقق من صندوق الوارد أو البريد العشوائي.');
    } catch (error) {
      let userFacingMessage = 'حدث خطأ أثناء إرسال رابط إعادة التعيين.';
      switch (error.code) {
        case 'auth/invalid-email':
          userFacingMessage = 'صيغة البريد الإلكتروني غير صحيحة.';
          break;
        case 'auth/user-not-found':
          userFacingMessage = 'لا يوجد حساب مرتبط بهذا البريد الإلكتروني.';
          break;
        default:
          userFacingMessage = 'حدث خطأ غير متوقع. يرجى المحاولة لاحقًا.';
      }
      setErrorMessage(userFacingMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-box">
        <div className="logo-section">
          <img
            src="/logo.png"
            alt="الأجندة القضائية"
            className="legal-agenda-logo"
          />
        </div>

        {loading && <LoadingSpinner message="جاري تسجيل الدخول..." />}

        {errorMessage && <div className="error-message">{errorMessage}</div>}
        {successMessage && <div className="success-message">{successMessage}</div>}

        <form id="login-form" onSubmit={handleSubmit}>
          <div className="header-form">
            <span className="title">مرحباً بعودتك!</span>
            <span className="subtitle">اختر خياراً للمتابعة</span>
          </div>

          <input
            type="email"
            id="login-email"
            placeholder="البريد الإلكتروني"
            required={true}
            value={email}
            onChange={handleEmailChange}
            disabled={loading}
          />

          {showPassword && (
            <input
              type="password"
              id="login-password"
              placeholder="كلمة المرور"
              required={true}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
            />
          )}

          <button type="submit" disabled={loading || !showPassword}>
            {loading ? 'جاري التحقق...' : 'متابعة'}
          </button>

          <div className="division-or">
            <div className="h-line"></div>
            <span>أو</span>
            <div className="h-line"></div>
          </div>

          <button type="button" id="google-btn">متابعة مع Google</button>
          <button type="button" id="signup-btn" onClick={() => navigate('/signup')}>إنشاء حساب</button>

          {!showPassword && (
            <div className="forgot-password">
              <a href="#" onClick={(e) => { e.preventDefault(); handlePasswordReset(); }}>
                نسيت عنوان البريد الإلكتروني؟
              </a>
            </div>
          )}

          {showPassword && (
            <div className="password-actions">
              <div className="forgot-password">
                <a href="#" onClick={(e) => { e.preventDefault(); handlePasswordReset(); }}>
                  نسيت كلمة المرور؟
                </a>
              </div>
              <div className="back-to-email">
                <a href="#" onClick={(e) => { e.preventDefault(); setShowPassword(false); setPassword(''); }}>
                  ← تغيير البريد الإلكتروني
                </a>
              </div>
            </div>
          )}
        </form>
      </div>
    </div>
  );
}

export default Login;