import React from 'react';
import styles from './LoadingSpinner.module.css';

const LoadingSpinner = ({ message = "جاري التحميل..." }) => {
  return (
    <div className={styles.loadingContainer}>
      <div className={styles.loadingContent}>
        {/* الدائرة المتحركة الرئيسية */}
        <div className={styles.spinner}>
          <div className={styles.spinnerRing}></div>
          <div className={styles.spinnerRing}></div>
          <div className={styles.spinnerRing}></div>
          <div className={styles.spinnerRing}></div>
          {/* النقطة المتحركة */}
          <div className={styles.movingDot}></div>
        </div>
        
        {/* رسالة التحميل */}
        <div className={styles.messageContainer}>
          <p className={styles.loadingMessage}>{message}</p>
          <div className={styles.dots}>
            <span className={styles.dot}></span>
            <span className={styles.dot}></span>
            <span className={styles.dot}></span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoadingSpinner;