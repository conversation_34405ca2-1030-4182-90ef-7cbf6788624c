/* Simple Login Design */
@import '../styles/variables.css';

.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  padding: 20px;
}

.login-box {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

/* تطبيق الثيم الجديد على نموذج تسجيل الدخول */
#login-form {
  --primary-color: orangered;
  --gray-border-dark: #b9b9b9;
  --gray-light: #f2f3f6;
  --gray: #727586;
  --dark: #262626;

  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.logo-section {
  text-align: center;
  margin-bottom: 30px;
}

.legal-agenda-logo {
  width: 100px;
  height: auto;
  margin-bottom: 15px;
}

/* تطبيق الثيم الجديد على العناوين */
#login-form .header-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
}

#login-form .header-form .title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  line-height: 1.2;
}

#login-form .header-form .subtitle {
  color: var(--gray);
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 25px;
  line-height: 1.4;
}

/* تطبيق الثيم الجديد على الحقول */
#login-form input {
  padding: 12px 15px;
  border: 1px solid var(--gray-border-dark);
  border-radius: 5px;
  outline: none;
  transition: 0.2s ease-in-out;
  width: 100%;
  font-size: 16px;
  box-sizing: border-box;
  background-color: white;
  color: #333;
  margin-bottom: 15px;
}

#login-form input:focus {
  border-color: var(--primary-color);
}

/* روابط بسيطة */
.forgot-password {
  text-align: center;
  margin: 15px 0;
}

.password-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0;
  flex-wrap: wrap;
  gap: 15px;
}

.forgot-password a,
.back-to-email a {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 14px;
}

.forgot-password a:hover,
.back-to-email a:hover {
  text-decoration: underline;
}

/* تطبيق الثيم الجديد على الأزرار */
#login-form button {
  border: none;
  padding: 12px 15px;
  border-radius: 5px;
  cursor: pointer;
  transition: 0.1s ease-in-out;
  font-size: 16px;
  width: 100%;
  margin-bottom: 10px;
  font-weight: 500;
}

#login-form button[type="submit"] {
  background-color: var(--primary-color);
  color: #fff;
  margin-bottom: 20px;
}

#login-form button[type="submit"]:hover {
  opacity: 0.9;
}

#login-form button:disabled {
  background-color: var(--gray-light);
  color: var(--gray);
  cursor: not-allowed;
}

#login-form #google-btn {
  background-color: #fff;
  border: 1px solid var(--gray-border-dark);
  color: var(--dark);
}

#login-form #signup-btn {
  background-color: var(--dark);
  color: #fff;
}

.login-btn {
  /* الحفاظ على التصميم الأصلي مع استخدام المتغيرات الموحدة */
  background: linear-gradient(135deg, var(--primary-medium-blue) 0%, var(--primary-dark-blue) 100%);
  color: var(--white);
  padding: 18px 40px;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  transition: var(--btn-transition);
  min-height: 60px;


/* تطبيق الثيم الجديد على خط الفاصل */
#login-form .division-or {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 5px 0;
}

#login-form .division-or span {
  color: var(--gray);
  font-size: 14px;
}

#login-form .h-line {
  flex: 1;
  height: 1px;
  background-color: var(--gray-border-dark);
}

/* رسائل الخطأ والنجاح */
.error-message {
  color: #e53e3e;
  margin: 15px 0;
  padding: 12px 15px;
  background: #fed7d7;
  border-radius: 5px;
  font-size: 14px;
  text-align: center;
}

.success-message {
  color: #38a169;
  margin: 15px 0;
  padding: 12px 15px;
  background: #c6f6d5;
  border-radius: 5px;
  font-size: 14px;
  text-align: center;
}

/* تصميم متجاوب */
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .login-box {
    padding: 30px 20px;
  }

  .legal-agenda-logo {
    width: 100px;
  }

  #login-form .header-form .title {
    font-size: 24px;
  }
}


  font-size: 0.95rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(56, 161, 105, 0.1);
}

.success-message::before {
  content: "✅";
  margin-left: 10px;
  font-size: 1.1rem;
}

.loading-container {
  margin: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loader {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-purple-gray);
  border-top: 4px solid var(--primary-medium-blue);
  border-radius: 50%;
  animation: modernSpin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

@keyframes modernSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--dark-blue-gray);
  font-size: 1rem;
  font-weight: 500;
}

.footer-links {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 20px;
  font-size: 0.85rem;
}

.footer-links a {
  color: var(--light-purple-gray);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  opacity: 0.8;
}

.footer-links a:hover {
  color: var(--white);
  opacity: 1;
}

/* تأثيرات للأجهزة اللوحية */
@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
  }
  
  .left-section {
    flex: none;
    min-height: 40vh;
    padding: 30px 20px;
  }
  
  .legal-icon {
    font-size: 80px;
    margin-bottom: 20px;
  }
  
  .legal-title {
    font-size: 2rem;
    margin-bottom: 15px;
  }
  
  .legal-subtitle {
    font-size: 1rem;
    margin-bottom: 20px;
  }
  
  .right-section {
    flex: none;
    min-height: 60vh;
    padding: 20px;
  }
  
  .login-box {
    max-width: 500px;
    padding: 40px 30px;
  }
  
  .decorative-elements {
    display: none;
  }
  
  .footer-links {
    position: relative;
    bottom: auto;
    right: auto;
    margin-top: 20px;
    justify-content: center;
  }
}

/* تأثيرات للهواتف */
@media (max-width: 768px) {
  .left-section {
    min-height: 35vh;
    padding: 20px 15px;
  }
  
  .legal-icon.main-icon {
    font-size: 60px;
    margin-bottom: 15px;
  }
  
  .mini-icons {
    gap: 15px;
  }
  
  .mini-icon {
    font-size: 18px;
  }
  
  .feature-list {
    margin-top: 25px;
    gap: 10px;
  }
  
  .feature-item {
    font-size: 0.9rem;
    padding: 8px 15px;
  }
  
  .legal-title {
    font-size: 1.6rem;
    margin-bottom: 10px;
  }
  
  .legal-subtitle {
    font-size: 0.9rem;
    margin-bottom: 15px;
  }
  
  .login-box {
    padding: 30px 25px;
    margin: 0 10px;
    max-width: none;
  }
  
  .login-title {
    font-size: 1.8rem;
  }
  
  .login-subtitle {
    font-size: 0.9rem;
    margin-bottom: 25px;
  }
  
  .form-group input[type='email'],
  .form-group input[type='password'],
  .single-input {
    padding: 18px 20px;
    font-size: 1rem;
    min-height: 60px;
  }
  
  .password-actions {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .footer-links {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .left-section {
    min-height: 30vh;
    padding: 15px 10px;
  }
  
  .legal-icon.main-icon {
    font-size: 50px;
    margin-bottom: 10px;
  }
  
  .mini-icons {
    gap: 10px;
    margin-top: 10px;
  }
  
  .mini-icon {
    font-size: 16px;
  }
  
  .feature-list {
    margin-top: 20px;
    gap: 8px;
  }
  
  .feature-item {
    font-size: 0.8rem;
    padding: 6px 12px;
    text-align: center;
  }
  
  .legal-title {
    font-size: 1.4rem;
    margin-bottom: 8px;
  }
  
  .legal-subtitle {
    font-size: 0.8rem;
    margin-bottom: 10px;
  }
  
  .login-box {
    padding: 25px 20px;
    margin: 0 5px;
    border-radius: 15px;
  }
  
  .legal-agenda-logo {
    width: 140px;
  }
  
  .login-title {
    font-size: 1.5rem;
  }
  
  .login-subtitle {
    font-size: 0.85rem;
  }
  
  .form-group input[type='email'],
  .form-group input[type='password'],
  .single-input {
    padding: 16px 18px;
    font-size: 16px; /* يمنع التكبير في iOS */
    min-height: 55px;
  }
  
  .login-btn {
    padding: 16px 30px;
    font-size: 1rem;
  }
}

/* تحسينات إضافية */
.login-box:focus-within {
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 35px var(--shadow-light);
}

.form-group input:focus {
  border-color: var(--primary-medium-blue);
  box-shadow: 0 0 0 3px rgba(76, 104, 192, 0.1);
  outline: none;
}

.login-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(76, 104, 192, 0.3);
}

/* تأثيرات smooth للانتقالات */
* {
  box-sizing: border-box;
}

.login-container * {
  transition: all 0.3s ease;
}