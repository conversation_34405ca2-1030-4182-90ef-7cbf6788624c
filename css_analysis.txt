تحليل استخدام CSS في CaseDetails.module.css
=============================================

الكلاسات المستخدمة في CaseInfoGroups.jsx:
- partiesCard
- identificationCard
- locationCard
- longTextContainer
- longTextFull
- longTextPreview
- moreTextIndicator
- valueTextHorizontal
- dataFieldHorizontal
- fieldLabelHorizontal
- fieldValueContainerHorizontal
- editFieldContainerHorizontal
- fullCaseNumberEditContainer
- caseNumberFieldsContainer
- caseNumberField
- inlineLabel
- editInput
- inputError
- errorText
- editActionsHorizontal
- saveEditButton
- cancelEditButton
- textareaInput
- valueWithActionHorizontal
- booleanValue
- trueValue
- falseValue
- referralButtonHorizontal
- enabledReferralButton
- disabledReferralButton
- editIconButtonHorizontal
- coloredCardsContainerHorizontal
- simpleCard
- simpleCardHeader
- cardPrimaryContent
- cardExpandedContent
- expanded
- expandedContentDivider
- transferDegreeSection
- transferDegreeBox
- partiesTheme
- identificationTheme
- transferHeader
- transferTitleSection
- transferIcon
- transferTitle
- transferContent
- transferInfoRow
- transferLabel
- transferValue
- transferButton
- expandToggle
- transferConfirmationOverlay
- transferConfirmationDialog
- transferConfirmationIcon
- transferConfirmationTitle
- transferConfirmationMessage
- transferConfirmationActions
- transferConfirmButton
- transferCancelButton
- statusOptionsContainer
- questionText
- statusOptions
- statusOptionButton
- selected
- dateInputContainer
- dateLabel
- dateInput
- errorMessage
- verdictButtonsContainer
- verdictButton
- selectedButton
- verdictButtonIcon

الكلاسات الموجودة في CSS ولكن غير مستخدمة:
- pageWrapper (خاص بصفحة كاملة وليس مكون)
- mainContainer (خاص بصفحة كاملة وليس مكون)
- headerSection (خاص بصفحة كاملة وليس مكون)  
- caseTitle (خاص بصفحة كاملة وليس مكون)
- coloredCard (محل بـ simpleCard)
- cardHeader (محل بـ simpleCardHeader)
- cardHeaderContent (لم يعد مستخدم)
- cardTitle (لم يعد مستخدم)
- expandButton (مكرر - موجود ولكن مختلف)
- timelineCard (تم نقله لمكون منفصل)
- timelineScrollContainer (تم نقله لمكون منفصل)
- timelineEntry (تم نقله لمكون منفصل)
- timelineDate (تم نقله لمكون منفصل)
- timelineDescription (تم نقله لمكون منفصل)
- noTimeline (تم نقله لمكون منفصل)
- inlineInput (استخدام آخر - قد يكون مستخدم في مكان آخر)
- inlineSelect (استخدام آخر - قد يكون مستخدم في مكان آخر)
- transferErrorMessage (محل بـ errorMessage)
- transferButtonGroup (لم يعد مستخدم)
- confirmReferralButton (لم يعد مستخدم)
- cancelReferralButton (لم يعد مستخدم)
- closeButton (استخدام آخر - قد يكون مستخدم في مكان آخر)
- circularButton وجميع الأزرار الدائرية (لم تعد مستخدمة)
- buttonGroup (لم يعد مستخدم)
- confirmationQuestion (لم يعد مستخدم)
- actionsSection (خاص بصفحة أخرى)
- addOptions (خاص بصفحة أخرى)
- addDeferralButton (خاص بصفحة أخرى)
- addActionOptionButton (خاص بصفحة أخرى)
- locationTheme (جزئياً مستخدم)

وكلاسات أخرى كثيرة...