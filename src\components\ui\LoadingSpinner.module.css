/* حاوي التحميل الرئيسي - يغطي كامل الشاشة */
.loadingContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

/* تأثير الخلفية المتحركة */
.loadingContainer::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(1, 72, 113, 0.02) 0%, transparent 50%);
  animation: backgroundFloat 8s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.05) rotate(180deg);
    opacity: 0.6;
  }
}

/* محتوى التحميل */
.loadingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2.5rem;
  text-align: center;
  position: relative;
  z-index: 1;
}

/* الدائرة المتحركة */
.spinner {
  position: relative;
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* حلقات الدائرة */
.spinnerRing {
  position: absolute;
  border: 3px solid transparent;
  border-radius: 50%;
  /* استخدام المتغير الموحد للأنيميشن */
  animation: var(--loading-animation);
  animation-duration: 2.5s;
}

.spinnerRing:nth-child(1) {
  width: 100px;
  height: 100px;
  border-top-color: #014871;
  border-right-color: rgba(1, 72, 113, 0.2);
  animation-delay: 0s;
  filter: drop-shadow(0 0 10px rgba(1, 72, 113, 0.3));
}

.spinnerRing:nth-child(2) {
  width: 75px;
  height: 75px;
  border-right-color: #4a8fa3;
  border-bottom-color: rgba(74, 143, 163, 0.2);
  animation-delay: -0.6s;
  animation-direction: reverse;
  filter: drop-shadow(0 0 8px rgba(74, 143, 163, 0.3));
}

.spinnerRing:nth-child(3) {
  width: 50px;
  height: 50px;
  border-bottom-color: #7bb3c7;
  border-left-color: rgba(123, 179, 199, 0.2);
  animation-delay: -1.2s;
  filter: drop-shadow(0 0 6px rgba(123, 179, 199, 0.3));
}

.spinnerRing:nth-child(4) {
  width: 25px;
  height: 25px;
  border-left-color: #a8d0e6;
  border-top-color: rgba(168, 208, 230, 0.2);
  animation-delay: -1.8s;
  animation-direction: reverse;
  filter: drop-shadow(0 0 4px rgba(168, 208, 230, 0.3));
}

/* النقطة المتحركة */
.movingDot {
  position: absolute;
  width: 8px;
  height: 8px;
  background: linear-gradient(45deg, #014871, #4a8fa3);
  border-radius: 50%;
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  animation: dotOrbit 2.5s linear infinite;
  box-shadow: 
    0 0 10px rgba(1, 72, 113, 0.6),
    0 0 20px rgba(1, 72, 113, 0.4),
    0 0 30px rgba(1, 72, 113, 0.2);
}

@keyframes dotOrbit {
  0% {
    transform: translateX(-50%) rotate(0deg) translateY(-50px) rotate(0deg);
  }
  100% {
    transform: translateX(-50%) rotate(360deg) translateY(-50px) rotate(-360deg);
  }
}

/* أنيميشن الدوران - تم نقلها إلى variables.css لتجنب التكرار */

/* حاوي الرسالة */
.messageContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

/* رسالة التحميل */
.loadingMessage {
  font-size: 1.3rem;
  font-weight: 600;
  color: #014871;
  margin: 0;
  direction: rtl;
  text-align: center;
  text-shadow: 0 2px 4px rgba(1, 72, 113, 0.1);
  letter-spacing: 0.5px;
  animation: textPulse 2s ease-in-out infinite;
}

@keyframes textPulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(0.98);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

/* النقاط المتحركة تحت النص */
.dots {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.dot {
  width: 8px;
  height: 8px;
  background: #4a8fa3;
  border-radius: 50%;
  animation: dotBounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

.dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes dotBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* تأثير الهالة حول الدائرة */
.spinner::before {
  content: '';
  position: absolute;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(1, 72, 113, 0.03) 0%, rgba(74, 143, 163, 0.02) 50%, transparent 70%);
  animation: halo 4s ease-in-out infinite;
  z-index: -1;
}

@keyframes halo {
  0%, 100% {
    transform: scale(0.9);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.2;
  }
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .spinner {
    width: 80px;
    height: 80px;
  }
  
  .spinnerRing:nth-child(1) {
    width: 80px;
    height: 80px;
  }
  
  .spinnerRing:nth-child(2) {
    width: 60px;
    height: 60px;
  }
  
  .spinnerRing:nth-child(3) {
    width: 40px;
    height: 40px;
  }
  
  .spinnerRing:nth-child(4) {
    width: 20px;
    height: 20px;
  }
  
  .loadingMessage {
    font-size: 1.1rem;
  }
  
  .spinner::before {
    width: 100px;
    height: 100px;
  }
  
  .loadingContent {
    gap: 2rem;
  }
  
  .movingDot {
    width: 6px;
    height: 6px;
  }
  
  .dot {
    width: 6px;
    height: 6px;
  }
}

@media (max-width: 480px) {
  .spinner {
    width: 60px;
    height: 60px;
  }
  
  .spinnerRing:nth-child(1) {
    width: 60px;
    height: 60px;
  }
  
  .spinnerRing:nth-child(2) {
    width: 45px;
    height: 45px;
  }
  
  .spinnerRing:nth-child(3) {
    width: 30px;
    height: 30px;
  }
  
  .spinnerRing:nth-child(4) {
    width: 15px;
    height: 15px;
  }
  
  .loadingMessage {
    font-size: 1rem;
  }
  
  .loadingContent {
    gap: 1.5rem;
  }
  
  .movingDot {
    width: 5px;
    height: 5px;
  }
  
  .dot {
    width: 5px;
    height: 5px;
  }
}
.loadingVideo {
  width: 120px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.2);
  object-fit: contain;
}
