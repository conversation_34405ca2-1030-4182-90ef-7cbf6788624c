/* حاوي التحميل الرئيسي - يغطي كامل الشاشة */
.loadingContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

/* تأثير الخلفية المتحركة */
.loadingContainer::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(1, 72, 113, 0.02) 0%, transparent 50%);
  animation: backgroundFloat 8s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.05) rotate(180deg);
    opacity: 0.6;
  }
}

/* محتوى التحميل */
.loadingContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2.5rem;
  text-align: center;
  position: relative;
  z-index: 1;
}

/* الحاوي الجديد للرسوم المتحركة */
.spinner {
  position: relative;
  width: 80px;
  height: 100px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  gap: 10px;
}

/* المكعبات المتحركة */
.block {
  position: relative;
  box-sizing: border-box;
  width: 12px;
  height: 12px;
  border-radius: 3px;
  background: #710101;
  display: block;
}

.block:nth-child(1), .block:nth-child(5), .block:nth-child(9), .block:nth-child(13) {
  animation: wave_61 2s ease .0s infinite;
}

.block:nth-child(2), .block:nth-child(6), .block:nth-child(10), .block:nth-child(14) {
  animation: wave_61 2s ease .2s infinite;
}

.block:nth-child(3), .block:nth-child(7), .block:nth-child(11), .block:nth-child(15) {
  animation: wave_61 2s ease .4s infinite;
}

.block:nth-child(4), .block:nth-child(8), .block:nth-child(12), .block:nth-child(16) {
  animation: wave_61 2s ease .6s infinite;
}

@keyframes wave_61 {
  0% {
    top: 0;
    opacity: 1;
  }

  50% {
    top: 30px;
    opacity: .2;
  }

  100% {
    top: 0;
    opacity: 1;
  }
}



/* حاوي الرسالة */
.messageContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

/* رسالة التحميل */
.loadingMessage {
  font-size: 1.3rem;
  font-weight: 600;
  color: #014871;
  margin: 0;
  direction: rtl;
  text-align: center;
  text-shadow: 0 2px 4px rgba(1, 72, 113, 0.1);
  letter-spacing: 0.5px;
  animation: textPulse 2s ease-in-out infinite;
}

@keyframes textPulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(0.98);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

/* النقاط المتحركة تحت النص */
.dots {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.dot {
  width: 8px;
  height: 8px;
  background: #4a8fa3;
  border-radius: 50%;
  animation: dotBounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

.dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes dotBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}



/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .spinner {
    width: 70px;
    height: 80px;
    gap: 8px;
  }

  .block {
    width: 10px;
    height: 10px;
  }

  .loadingMessage {
    font-size: 1.1rem;
  }

  .loadingContent {
    gap: 2rem;
  }

  .dot {
    width: 6px;
    height: 6px;
  }
}

@media (max-width: 480px) {
  .spinner {
    width: 60px;
    height: 70px;
    gap: 6px;
  }

  .block {
    width: 8px;
    height: 8px;
  }

  .loadingMessage {
    font-size: 1rem;
  }

  .loadingContent {
    gap: 1.5rem;
  }

  .dot {
    width: 5px;
    height: 5px;
  }
}
.loadingVideo {
  width: 120px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.2);
  object-fit: contain;
}
