/* Search Container */
.searchContainer {
  position: relative;
  width: 100%;
  max-width: 640px;
  margin: 0 auto;
}

.searchBox {
  position: relative;
  width: 100%;
}

.searchIcon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  font-size: 16px;
  z-index: 1;
}

.searchInput {
  width: 100%;
  padding: 8px 36px 8px 10px;
  border: 1px solid var(--neutral-200);
  border-radius: 30px;
  font-size: 14px;
  background-color: var(--page-background);
  color: var(--current-text-primary);
  transition: var(--transition-normal);
  height: 52px;
  box-shadow:
    0 4px 15px var(--shadow-light),
    0 10px 30px var(--shadow-light);
  backdrop-filter: var(--current-backdrop-blur);
  box-sizing: border-box;
}

.searchInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow:
    0 25px 70px var(--shadow-medium),
    0 15px 40px var(--shadow-light);
  background-color: var(--page-background);
}

.searchInput::placeholder {
  color: var(--current-text-tertiary);
  font-size: 14px;
}

.loadingSpinner {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinnerIcon {
  font-size: 14px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Dropdown Styles */
.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--page-background);
  border: 1px solid var(--neutral-200);
  border-radius: 16px;
  box-shadow: 
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  backdrop-filter: var(--current-backdrop-blur);
  z-index: 1000;
  margin-top: 8px;
  max-height: 400px;
  overflow-y: auto;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdownHeader {
  padding: 12px 16px;
  border-bottom: 1px solid var(--neutral-200);
  background: var(--neutral-50);
  font-size: 12px;
  font-weight: 600;
  color: var(--current-text-secondary);
  border-radius: 16px 16px 0 0;
}

.noResults {
  padding: 20px 16px;
  text-align: center;
  color: var(--current-text-secondary);
  font-size: 14px;
  font-style: italic;
}

.suggestionItem {
  padding: 12px 16px;
  border-bottom: 1px solid var(--neutral-100);
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--page-background);
}

.suggestionItem:hover,
.suggestionItem.selected {
  background: var(--neutral-50);
  transform: translateX(-2px);
}

.suggestionItem.selected {
  background: var(--primary-color);
  color: white;
}

.suggestionItem.selected .suggestionTitle,
.suggestionItem.selected .caseNumber {
  color: white;
}

.suggestionItem.selected .suggestionIcon {
  color: rgba(255, 255, 255, 0.9);
}

.suggestionItem.selected .detailItem {
  color: rgba(255, 255, 255, 0.8);
}

.suggestionItem.selected .detailIcon {
  color: rgba(255, 255, 255, 0.7);
}

.suggestionItem:last-child {
  border-bottom: none;
}

.suggestionMain {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
  justify-content: space-between;
}

.suggestionTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--current-text-primary);
  flex: 1;
  min-width: 0;
}

.suggestionIcon {
  color: var(--primary-color);
  font-size: 14px;
  flex-shrink: 0;
}

.caseNumber {
  font-size: 16px;
  color: var(--current-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.suggestionDetails {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0;
}

.detailItem {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: var(--current-text-secondary);
  white-space: nowrap;
}

.detailIcon {
  font-size: 12px;
  color: var(--current-text-tertiary);
  flex-shrink: 0;
}

.highlight {
  background: rgba(255, 235, 59, 0.3);
  color: var(--current-text-primary);
  font-weight: 600;
  padding: 1px 2px;
  border-radius: 2px;
}



/* Responsive Design */
@media (max-width: 768px) {
  .searchContainer {
    max-width: 100%;
  }

  .searchInput {
    height: 48px;
    font-size: 16px; /* منع التكبير في iOS */
  }

  .dropdown {
    max-height: 300px;
  }

  .suggestionMain {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .suggestionTitle {
    width: 100%;
  }

  .suggestionDetails {
    width: 100%;
    justify-content: flex-start;
    gap: 12px;
  }

  .caseNumber {
    font-size: 14px;
  }

  .detailItem {
    font-size: 12px;
  }

  .detailIcon {
    font-size: 10px;
  }
}

/* Dark Theme Support */
body.theme-dark .dropdown {
  background: var(--dark-bg-secondary);
  border-color: var(--dark-border-primary);
}

body.theme-dark .dropdownHeader {
  background: var(--dark-bg-tertiary);
  border-color: var(--dark-border-primary);
}

body.theme-dark .suggestionItem:hover {
  background: var(--dark-bg-tertiary);
}

/* Liquid Theme Support */
body.theme-liquid .dropdown {
  backdrop-filter: blur(20px) saturate(120%);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

body.theme-liquid .dropdownHeader {
  background: rgba(248, 250, 252, 0.8);
}

body.theme-liquid .suggestionItem:hover {
  background: rgba(248, 250, 252, 0.6);
}

/* Scrollbar Styling */
.dropdown::-webkit-scrollbar {
  width: 6px;
}

.dropdown::-webkit-scrollbar-track {
  background: var(--neutral-100);
  border-radius: 3px;
}

.dropdown::-webkit-scrollbar-thumb {
  background: var(--neutral-300);
  border-radius: 3px;
}

.dropdown::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-400);
}
